<template>
  <JtConfirmModal v-model:open="visible" title="SSH远程开发使用方式" :width="600" footer-type="confirm" ok-text="关闭" :icon-color="'#1890ff'" @cancel="handleCancel" @ok="handleCancel">
    <template #icon>
      <span></span>
    </template>
    <a-spin :spinning="loading">
      <div class="ssh-modal-content">
        <!-- 未创建密钥场景 -->
        <div v-if="!sshKeyExists" class="no-key-section">
          <div class="guide-text">
            <p>{{ guideText }}</p>
            <p v-if="!isLeader" class="contact-leader">请联系项目空间负责人进行创建。</p>
          </div>
          <div v-if="isLeader" class="action-buttons">
            <a-button type="primary" :loading="createLoading" @click="handleCreateKey"> 创建密钥 </a-button>
          </div>
        </div>

        <!-- 已创建密钥场景 -->
        <div v-else class="key-exists-section">
          <div class="guide-text">
            <p>{{ guideText }}</p>
          </div>

          <div class="key-info">
            <div class="info-item">
              <span class="label">SSH密钥指纹：</span>
              <span class="value">{{ sshKeyInfo.fingerprint }}</span>
            </div>
            <div class="info-item">
              <span class="label">密钥更新时间：</span>
              <span class="value">{{ formatTime(sshKeyInfo.updateTime) }}</span>
            </div>
          </div>

          <div class="action-buttons">
            <a-button type="primary" @click="handleDownloadKey"> 下载私钥 </a-button>
            <a-button v-if="isLeader" @click="handleRecreateKey"> 重新创建密钥 </a-button>
            <a-button type="link" @click="openHelpCenter"> 帮助中心 </a-button>
          </div>
        </div>
      </div>
    </a-spin>

    <!-- SMS验证码弹窗 -->
    <SmsVerificationModal v-model:visible="showSmsModal" @verify-success="onSmsVerifySuccess" />

    <!-- 重新创建确认弹窗 -->
    <JtConfirmModal v-model:open="showRecreateConfirm" title="确认重新创建" :width="400" footer-type="confirm" ok-text="确认" cancel-text="取消" @ok="confirmRecreateKey" @cancel="showRecreateConfirm = false">
      <p>重新创建将导致原有密钥失效，请谨慎操作。</p>
    </JtConfirmModal>
  </JtConfirmModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { getCurrentRole } from '../utils';
import { ROLE_KEYS } from '@/constants/projectSpace.js';
import { sshApi } from '@/apis';
import { getFormatTime } from '@/utils/index.js';
import SmsVerificationModal from './SmsVerificationModal.vue';

const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
});

const store = useStore();
const visible = defineModel('visible');

const loading = ref(false);
const createLoading = ref(false);
const sshKeyExists = ref(false);
const sshKeyInfo = ref({});
const isLeader = ref(false);
const showSmsModal = ref(false);
const showRecreateConfirm = ref(false);
const sidebarStatus = ref({});

// 获取用户角色
const getUserRole = async () => {
  try {
    const { role } = await getCurrentRole();
    isLeader.value = role === ROLE_KEYS.LEADER;
  } catch (error) {
    console.error('获取用户角色失败:', error);
  }
};

// 获取侧边栏状态
const getSidebarStatus = async () => {
  try {
    const res = await sshApi.getSidebarStatus();
    if (res.code === 0) {
      sidebarStatus.value = res.data || {};
    }
  } catch (error) {
    console.error('获取侧边栏状态失败:', error);
  }
};

// 动态引导文案
const guideText = computed(() => {
  const devEnvVisible = sidebarStatus.value.devEnv !== false;
  const trainTaskVisible = sidebarStatus.value.trainTask !== false;

  if (!devEnvVisible && trainTaskVisible) {
    return sshKeyExists.value ? '您可以使用SSH密钥连接到训练任务进行远程开发。' : '当前未配置SSH密钥，配置后可连接到训练任务进行远程开发。';
  } else if (devEnvVisible && !trainTaskVisible) {
    return sshKeyExists.value ? '您可以使用SSH密钥连接到开发环境进行远程开发。' : '当前未配置SSH密钥，配置后可连接到开发环境进行远程开发。';
  } else {
    return sshKeyExists.value ? '您可以使用SSH密钥连接到开发环境和训练任务进行远程开发。' : '当前未配置SSH密钥，配置后可连接到开发环境和训练任务进行远程开发。';
  }
});

// 获取SSH密钥信息
const getSshKeyInfo = async () => {
  try {
    loading.value = true;
    const res = await sshApi.getSshKeyInfo();
    if (res.code === 0) {
      sshKeyExists.value = !!res.data;
      if (sshKeyExists.value) {
        sshKeyInfo.value = res.data;
      }
    } else {
      message.error(res.msg || '获取SSH密钥信息失败');
    }
  } catch (error) {
    console.error('获取SSH密钥信息失败:', error);
    message.error('获取SSH密钥信息失败');
  } finally {
    loading.value = false;
  }
};

// 创建密钥
const handleCreateKey = async () => {
  try {
    createLoading.value = true;
    const res = await sshApi.createSshKey();
    // 处理blob响应
    if (res.headers && res.headers['content-disposition']) {
      const filename = extractFilename(res.headers['content-disposition']) || `${store.state.projectInfo.name || 'project'}_ssh_key`;
      downloadBlob(res.data, filename);
      message.success('创建成功');
      // 刷新密钥信息
      await getSshKeyInfo();
    } else {
      message.error('创建失败');
    }
  } catch (error) {
    console.error('创建密钥失败:', error);
    message.error('创建密钥失败');
  } finally {
    createLoading.value = false;
  }
};

// 下载私钥
const handleDownloadKey = () => {
  showSmsModal.value = true;
};

// SMS验证成功回调
const onSmsVerifySuccess = async (code) => {
  try {
    const res = await sshApi.downloadSshKey({ code });
    // 处理blob响应
    if (res.headers && res.headers['content-disposition']) {
      const filename = extractFilename(res.headers['content-disposition']) || `${store.state.projectInfo.name || 'project'}_ssh_key`;
      downloadBlob(res.data, filename);
      showSmsModal.value = false;
    } else {
      throw new Error('下载失败');
    }
  } catch (error) {
    console.error('下载私钥失败:', error);
    throw error; // 重新抛出错误，让SMS组件处理
  }
};

// 重新创建密钥
const handleRecreateKey = () => {
  showRecreateConfirm.value = true;
};

// 确认重新创建
const confirmRecreateKey = async () => {
  showRecreateConfirm.value = false;
  await handleCreateKey();
};

// 打开帮助中心
const openHelpCenter = () => {
  // TODO: 替换为实际的帮助中心URL
  window.open('https://help.example.com/ssh-guide', '_blank');
};

// 下载Blob
const downloadBlob = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
};

// 从Content-Disposition头中提取文件名
const extractFilename = (contentDisposition) => {
  const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
  if (matches != null && matches[1]) {
    return matches[1].replace(/['"]/g, '');
  }
  return null;
};

// 格式化时间
const formatTime = (time) => {
  return getFormatTime(time);
};

// 关闭弹窗
const handleCancel = () => {
  visible.value = false;
};

// 监听弹窗显示状态
watch(visible, async (val) => {
  if (val) {
    await Promise.all([getUserRole(), getSidebarStatus(), getSshKeyInfo()]);
  }
});
</script>

<style lang="less" scoped>
.ssh-modal-content {
  .guide-text {
    margin-bottom: 20px;

    p {
      margin-bottom: 8px;
      color: rgba(0, 20, 26, 0.7);
      line-height: 1.5;
    }

    .contact-leader {
      color: rgba(0, 20, 26, 0.45);
    }
  }

  .key-info {
    background: rgba(0, 20, 26, 0.02);
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 20px;

    .info-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: rgba(0, 20, 26, 0.7);
        min-width: 120px;
      }

      .value {
        color: #00141a;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;

    .ant-btn {
      min-width: 100px;
    }
  }
}
</style>
