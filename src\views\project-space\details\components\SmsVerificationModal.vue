<template>
  <a-modal
    v-model:open="visible"
    title="短信验证"
    :width="400"
    @ok="handleVerify"
    @cancel="handleCancel"
  >
    <div class="sms-verification">
      <div class="form-item">
        <label>验证码：</label>
        <div class="input-group">
          <a-input
            v-model:value="verificationCode"
            placeholder="请输入验证码"
            :status="inputStatus"
            @keyup.enter="handleVerify"
          />
          <a-button
            :disabled="countdown > 0"
            :loading="sendingCode"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
          </a-button>
        </div>
      </div>
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { sshApi } from '@/apis';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'verify-success']);

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

const verificationCode = ref('');
const errorMessage = ref('');
const inputStatus = ref('');
const sendingCode = ref(false);
const countdown = ref(0);
const countdownTimer = ref(null);

// 发送验证码
const sendVerificationCode = async () => {
  try {
    sendingCode.value = true;
    const res = await sshApi.sendSmsCode();
    
    if (res.code === 0) {
      message.success('验证码发送成功');
      startCountdown();
      clearError();
    } else {
      handleSmsError(res.code, res.msg);
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    message.error('发送验证码失败');
  } finally {
    sendingCode.value = false;
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }
  }, 1000);
};

// 验证验证码
const handleVerify = async () => {
  if (!verificationCode.value.trim()) {
    showError('请输入验证码');
    return;
  }

  try {
    await emit('verify-success', verificationCode.value);
    // 如果没有抛出错误，说明验证成功
    clearError();
  } catch (error) {
    handleVerifyError(error);
  }
};

// 处理短信发送错误
const handleSmsError = (code, msg) => {
  const errorMap = {
    130235: '用户未绑定手机号，无法发送验证码',
    130236: '短信服务异常，请稍后再试',
    998611: '验证码发送过于频繁',
    998612: '未发送验证码',
  };
  
  const errorMsg = errorMap[code] || msg || '发送验证码失败';
  message.error(errorMsg);
};

// 处理验证错误
const handleVerifyError = (error) => {
  const errorMsg = error.message || '验证失败';
  
  // 检查是否是验证码相关错误
  if (errorMsg.includes('验证码')) {
    showError(errorMsg);
  } else {
    message.error(errorMsg);
  }
};

// 显示错误信息
const showError = (msg) => {
  errorMessage.value = msg;
  inputStatus.value = 'error';
};

// 清除错误信息
const clearError = () => {
  errorMessage.value = '';
  inputStatus.value = '';
};

// 关闭弹窗
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  verificationCode.value = '';
  clearError();
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
    countdown.value = 0;
  }
};

// 监听弹窗状态
watch(visible, (val) => {
  if (!val) {
    resetForm();
  }
});

// 监听验证码输入，清除错误状态
watch(verificationCode, () => {
  if (errorMessage.value) {
    clearError();
  }
});
</script>

<style lang="less" scoped>
.sms-verification {
  .form-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      color: #00141a;
      font-weight: 500;
    }
    
    .input-group {
      display: flex;
      gap: 8px;
      
      .ant-input {
        flex: 1;
      }
      
      .ant-btn {
        min-width: 100px;
        white-space: nowrap;
      }
    }
  }
  
  .error-message {
    color: #ff4d4f;
    font-size: 14px;
    margin-top: 8px;
  }
}
</style>
